#!/usr/bin/env python3

import requests
import json

def test_health_ai(question):
    """Test the ArogyaMitraAI health check API"""
    print(f"\nTesting ArogyaMitraAI with question: '{question}'")
    print("-" * 60)
    
    url = "http://localhost:5000/ask"
    payload = {
        "question": question
    }
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        data = response.json()
        
        if response.status_code == 200:
            if "response" in data and "summary" in data:
                print("SUCCESS: ArogyaMitraAI is working perfectly!")
                print(f"AI Response length: {len(data['response'])} characters")
                print(f"Summary length: {len(data['summary'])} characters")
                
                print("\nAI Response Preview:")
                print("-" * 40)
                print(data['response'][:200] + "..." if len(data['response']) > 200 else data['response'])
                
                print("\nSummary Preview:")
                print("-" * 40)
                print(data['summary'][:200] + "..." if len(data['summary']) > 200 else data['summary'])
                
            elif "error" in data:
                print(f"ERROR: {data['error']}")
            else:
                print(f"WARNING: Unexpected response format: {data}")
        else:
            print(f"ERROR: HTTP Error {response.status_code}: {data}")
            
    except requests.exceptions.ConnectionError:
        print("ERROR: Cannot connect to backend. Make sure 'python backend.py' is running!")
    except requests.exceptions.Timeout:
        print("ERROR: Request timed out (this is normal for AI responses - try again)")
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    print("Testing ArogyaMitraAI (Health Check AI)")
    print("Make sure the backend is running with: python backend.py")
    print("Make sure you have GEMINI_API_KEY in your .env file")
    
    # Test health questions
    test_questions = [
        "I have fever and headache for 2 days",
        "I feel tired and have muscle pain",
        "What should I do for common cold?"
    ]
    
    for question in test_questions:
        test_health_ai(question)
        print("\n" + "="*60)
        
    print("\nTesting complete!")
    print("If you see AI responses and summaries, ArogyaMitraAI is working perfectly!")
    print("Visit: http://localhost:3000/health-check to use the web interface")

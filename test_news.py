#!/usr/bin/env python3

import requests
import json

def test_news_api(language):
    """Test the news API with given language"""
    print(f"\n📰 Testing health news for {language}")
    print("-" * 50)
    
    url = "http://localhost:5000/news"
    payload = {
        "language": language
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        data = response.json()
        
        if response.status_code == 200:
            if "news" in data:
                news_text = data["news"]
                print(f"✅ News generated successfully!")
                print(f"📊 Response length: {len(news_text)} characters")
                
                # Check if it contains proper article structure
                if "Title:" in news_text and "Description:" in news_text:
                    titles = news_text.count("Title:")
                    print(f"📋 Found {titles} news articles")
                    
                    # Show first article preview
                    first_title_start = news_text.find("Title:")
                    if first_title_start != -1:
                        preview = news_text[first_title_start:first_title_start + 200]
                        print(f"🔍 Preview:\n{preview}...")
                else:
                    print("⚠️  News generated but format may be incorrect")
                    print(f"Preview: {news_text[:200]}...")
                    
            elif "error" in data:
                print(f"❌ Error: {data['error']}")
            else:
                print(f"⚠️  Unexpected response format: {data}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {data}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure 'python backend.py' is running!")
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Testing GramAroghya News API")
    print("Make sure the backend is running with: python backend.py")
    
    # Test different languages
    test_languages = [
        "Hindi",
        "English", 
        "Gujarati",
        "Bengali"
    ]
    
    for language in test_languages:
        test_news_api(language)
        
    print("\n🏁 Testing complete!")
    print("If you see properly formatted news articles, the API is working correctly!")

from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/ask', methods=['POST'])
def ask():
    data = request.json
    question = data.get('question', '')
    
    # Mock response
    response = {
        "answer": "This is a mock response for your question: " + question,
        "sources": [
            "Mock source 1",
            "Mock source 2"
        ]
    }
    return jsonify(response)

@app.route('/doctors', methods=['POST'])
def find_doctors():
    data = request.json
    condition = data.get('condition', '')
    location = data.get('location', '')
    
    # Mock response
    response = {
        "doctors": [
            {
                "name": "Dr. Mock Doctor",
                "specialization": condition,
                "location": location,
                "rating": 4.5,
                "experience": "10 years"
            }
        ]
    }
    return jsonify(response)

@app.route('/health-centers', methods=['POST'])
def find_health_centers():
    data = request.json
    latitude = data.get('latitude', 0)
    longitude = data.get('longitude', 0)
    
    # Mock response
    response = {
        "health_centers": [
            {
                "name": "Mock Health Center",
                "address": "123 Mock Street",
                "distance": "2.5 km",
                "rating": 4.2,
                "services": ["General", "Emergency", "Pediatrics"]
            }
        ]
    }
    return jsonify(response)

@app.route('/news', methods=['POST'])
def get_news():
    data = request.json
    language = data.get('language', 'english')
    
    # Mock response
    response = {
        "articles": [
            {
                "title": "Mock Health News Article",
                "content": "This is a mock health news article in " + language,
                "source": "Mock News Source",
                "date": "2025-04-20"
            }
        ]
    }
    return jsonify(response)

if __name__ == "__main__":
    app.run(debug=True, port=5000)

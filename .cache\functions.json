{"expiry": 1753965255.7048385, "data": {"paraphrasing": {"id": "paraphrasing", "name": "Paraphrasing", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "Express the meaning of the writer or speaker or something written or spoken using different words."}}}, "language-identification": {"id": "language-identification", "name": "Language Identification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Detects the language in which a given text is written, aiding in multilingual platforms or content localization."}}}, "benchmark-scoring-asr": {"id": "benchmark-scoring-asr", "name": "Benchmark Scoring ASR", "description": null, "params": [{"name": "input", "code": "input", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "output", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "reference", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Benchmark Scoring ASR is a function that evaluates and compares the performance of automatic speech recognition systems by analyzing their accuracy, speed, and other relevant metrics against a standardized set of benchmarks."}}}, "multi-class-text-classification": {"id": "multi-class-text-classification", "name": "Multi Class Text Classification", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Multi Class Text Classification is a natural language processing task that involves categorizing a given text into one of several predefined classes or categories based on its content."}}}, "document-image-parsing": {"id": "document-image-parsing", "name": "Document Image Parsing", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["image-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Document Image Parsing is the process of analyzing and converting scanned or photographed images of documents into structured, machine-readable formats by identifying and extracting text, layout, and other relevant information."}}}, "audio-source-separation": {"id": "audio-source-separation", "name": "Audio Source Separation", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Audio", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "Audio Source Separation is the process of separating a mixture (e.g. a pop band recording) into isolated sounds from individual sources (e.g. just the lead vocals)."}}}, "keyword-spotting": {"id": "keyword-spotting", "name": "Keyword Spotting", "description": null, "params": [{"name": "audio", "code": "audio", "required": false, "dataType": "audio", "dataSubType": "audio"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Keyword Spotting is a function that enables the detection and identification of specific words or phrases within a stream of audio, often used in voice-activated systems to trigger actions or commands based on recognized keywords."}}}, "part-of-speech-tagging": {"id": "part-of-speech-tagging", "name": "Part of Speech Tagging", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Part of Speech Tagging is a natural language processing task that involves assigning each word in a sentence its corresponding part of speech, such as noun, verb, adjective, or adverb, based on its role and context within the sentence."}}}, "audio-intent-detection": {"id": "audio-intent-detection", "name": "Audio Intent Detection", "description": null, "params": [{"name": "audio", "code": "audio", "required": false, "dataType": "audio", "dataSubType": "audio"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Audio Intent Detection is a process that involves analyzing audio signals to identify and interpret the underlying intentions or purposes behind spoken words, enabling systems to understand and respond appropriately to human speech."}}}, "loglikelihood": {"id": "loglikelihood", "name": "Log Likelihood", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Probability", "code": "data", "defaultValue": [], "dataType": "number"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "number", "description": "The Log Likelihood function measures the probability of observing the given data under a specific statistical model by taking the natural logarithm of the likelihood function, thereby transforming the product of probabilities into a sum, which simplifies the process of optimization and parameter estimation."}}}, "multi-class-image-classification": {"id": "multi-class-image-classification", "name": "Multi Class Image Classification", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Multi Class Image Classification is a machine learning task where an algorithm is trained to categorize images into one of several predefined classes or categories based on their visual content."}}}, "language-identification-audio": {"id": "language-identification-audio", "name": "Language Identification Audio", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "The Language Identification Audio function analyzes audio input to determine and identify the language being spoken."}}}, "fact-checking": {"id": "fact-checking", "name": "Fact Checking", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Fact Checking is the process of verifying the accuracy and truthfulness of information, statements, or claims by cross-referencing with reliable sources and evidence."}}}, "table-question-answering": {"id": "table-question-answering", "name": "Table Question Answering", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The task of question answering over tables is given an input table (or a set of tables) T and a natural language question Q (a user query), output the correct answer A"}}}, "speech-classification": {"id": "speech-classification", "name": "Speech Classification", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Categorizes audio clips based on their content, aiding in content organization and targeted actions."}}}, "inverse-text-normalization": {"id": "inverse-text-normalization", "name": "Inverse Text Normalization", "description": null, "params": [{"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Inverse Text Normalization is the process of converting spoken or written language in its normalized form, such as numbers, dates, and abbreviations, back into their original, more complex or detailed textual representations."}}}, "asr-gender-classification": {"id": "asr-gender-classification", "name": "ASR Gender Classification", "description": null, "params": [{"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "label", "description": "The ASR Gender Classification function analyzes audio recordings to determine and classify the speaker's gender based on their voice characteristics."}}}, "text-embedding": {"id": "text-embedding", "name": "Text Embedding", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Embedding", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Text embedding is a process that converts text into numerical vectors, capturing the semantic meaning and contextual relationships of words or phrases, enabling machines to understand and analyze natural language more effectively."}}}, "audio-reconstruction": {"id": "audio-reconstruction", "name": "Audio Reconstruction", "description": null, "params": [{"name": "Segments", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Audio", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["audio-audio"], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "Audio Reconstruction is the process of restoring or recreating audio signals from incomplete, damaged, or degraded recordings to achieve a high-quality, accurate representation of the original sound."}}}, "viseme-generation": {"id": "viseme-generation", "name": "Viseme Generation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Viseme Generation is the process of creating visual representations of phonemes, which are the distinct units of sound in speech, to synchronize lip movements with spoken words in animations or virtual avatars."}}}, "topic-modeling": {"id": "topic-modeling", "name": "Topic Modeling", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Topic modeling is a type of statistical modeling for discovering the abstract “topics” that occur in a collection of documents."}}}, "detect-language-from-text": {"id": "detect-language-from-text", "name": "Detect Language From Text", "description": null, "params": [{"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "Langauge", "code": "data", "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Detect Language From Text"}}}, "extract-audio-from-video": {"id": "extract-audio-from-video", "name": "Extract Audio From Video", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video"}], "output": [{"name": "Audio", "code": "data", "dataType": "audio"}], "metadata": {"modalities": ["video-audio"], "metaData": {"InputType": "video", "OutputType": "audio", "description": "Isolates and extracts audio tracks from video files, aiding in audio analysis or transcription tasks."}}}, "scene-detection": {"id": "scene-detection", "name": "Scene Detection", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "image", "OutputType": "text", "description": "Scene detection is used for detecting transitions between shots in a video to split it into basic temporal segments."}}}, "text-to-image-generation": {"id": "text-to-image-generation", "name": "Text To Image Generation", "description": null, "params": [{"name": "Text Prompt", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Generated Image", "code": "data", "defaultValue": [], "dataType": "image"}], "metadata": {"modalities": ["text-image"], "metaData": {"InputType": "text", "OutputType": "image", "description": "Creates a visual representation based on textual input, turning descriptions into pictorial forms. Used in creative processes and content generation."}}}, "auto-mask-generation": {"id": "auto-mask-generation", "name": "Auto Mask Generation", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Auto-mask generation refers to the automated process of creating masks in image processing or computer vision, typically for segmentation tasks. A mask is a binary or multi-class image that labels different parts of an image, usually separating the foreground (objects of interest) from the background, or identifying specific object classes in an image."}}}, "question-answering": {"id": "question-answering", "name": "Question Answering", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "building systems that automatically answer questions posed by humans in a natural language usually from a given text"}}}, "facial-recognition": {"id": "facial-recognition", "name": "Facial Recognition", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "image", "OutputType": "label", "description": "A facial recognition system is a technology capable of matching a human face from a digital image or a video frame against a database of faces"}}}, "audio-language-identification": {"id": "audio-language-identification", "name": "Audio Language Identification", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "dataType": "audio", "dataSubType": "audio"}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Audio Language Identification is a process that involves analyzing an audio recording to determine the language being spoken."}}}, "text-to-video-generation": {"id": "text-to-video-generation", "name": "Text To Video Generation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "dataType": "text", "dataSubType": "text"}, {"name": "Language", "code": "language", "required": false, "dataType": "label", "dataSubType": "label"}], "output": [{"name": "Video", "code": "data", "dataType": "video"}], "metadata": {"modalities": ["text-video"], "metaData": {"InputType": "text", "OutputType": "video", "description": "Text To Video Generation is a process that converts written descriptions or scripts into dynamic, visual video content using advanced algorithms and artificial intelligence."}}}, "script-execution": {"id": "script-execution", "name": "Script Execution", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "Script Execution refers to the process of running a set of programmed instructions or code within a computing environment, enabling the automated performance of tasks, calculations, or operations as defined by the script."}}}, "image-impainting": {"id": "image-impainting", "name": "Image Impainting", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "image", "code": "image", "dataType": "image"}], "metadata": {"modalities": ["image-image"], "metaData": {"InputType": "image", "OutputType": "image", "description": "Image inpainting is a process that involves filling in missing or damaged parts of an image in a way that is visually coherent and seamlessly blends with the surrounding areas, often using advanced algorithms and techniques to restore the image to its original or intended appearance."}}}, "text-reconstruction": {"id": "text-reconstruction", "name": "Text Reconstruction", "description": null, "params": [{"name": "Segments", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Text Reconstruction is a process that involves piecing together fragmented or incomplete text data to restore it to its original, coherent form."}}}, "semantic-segmentation": {"id": "semantic-segmentation", "name": "Semantic Segmentation", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Semantic segmentation is a computer vision process that involves classifying each pixel in an image into a predefined category, effectively partitioning the image into meaningful segments based on the objects or regions they represent."}}}, "audio-emotion-detection": {"id": "audio-emotion-detection", "name": "Audio Emotion Detection", "description": null, "params": [{"name": "audio", "code": "audio", "required": false, "dataType": "audio", "dataSubType": "audio"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Audio Emotion Detection is a technology that analyzes vocal characteristics and patterns in audio recordings to identify and classify the emotional state of the speaker."}}}, "image-captioning": {"id": "image-captioning", "name": "Image Captioning", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["image-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Image Captioning is a process that involves generating a textual description of an image, typically using machine learning models to analyze the visual content and produce coherent and contextually relevant sentences that describe the objects, actions, and scenes depicted in the image."}}}, "split-on-linebreak": {"id": "split-on-linebreak", "name": "Split On Linebreak", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The \"Split On Linebreak\" function divides a given string into a list of substrings, using linebreaks (newline characters) as the points of separation."}}}, "style-transfer": {"id": "style-transfer", "name": "Style Transfer", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "image", "code": "image", "dataType": "image"}], "metadata": {"modalities": ["image-image"], "metaData": {"InputType": "image", "OutputType": "image", "description": "Style Transfer is a technique in artificial intelligence that applies the visual style of one image (such as the brushstrokes of a famous painting) to the content of another image, effectively blending the artistic elements of the first image with the subject matter of the second."}}}, "base-model": {"id": "base-model", "name": "Base-Model", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target Text", "code": "data", "defaultValue": true, "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The Base-Model function serves as a foundational framework designed to provide essential features and capabilities upon which more specialized or advanced models can be built and customized."}}}, "referenceless-audio-generation-metric": {"id": "referenceless-audio-generation-metric", "name": "Referenceless Audio Generation Metric", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["audio|text-number"], "metaData": {"InputType": "text", "OutputType": "text", "description": "The Referenceless Audio Generation Metric is a tool designed to evaluate the quality of generated audio content without the need for a reference or original audio sample for comparison."}}}, "entity-linking": {"id": "entity-linking", "name": "Entity Linking", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Domain", "code": "domain", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Associates identified entities in the text with specific entries in a knowledge base or database."}}}, "image-manipulation": {"id": "image-manipulation", "name": "Image Manipulation", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}, {"name": "Target Image", "code": "targetimage", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "image", "code": "image", "defaultValue": [], "dataType": "image"}], "metadata": {"modalities": ["image|image-image"], "metaData": {"InputType": "image", "OutputType": "image", "description": "Image Manipulation refers to the process of altering or enhancing digital images using various techniques and tools to achieve desired visual effects, correct imperfections, or transform the image's appearance."}}}, "metric-aggregation": {"id": "metric-aggregation", "name": "Metric Aggregation", "description": null, "params": [{"name": "Score Aggregation Data", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}], "output": [{"name": "Aggregates", "code": "data", "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "Metric Aggregation is a function that computes and summarizes numerical data by applying statistical operations, such as averaging, summing, or finding the minimum and maximum values, to provide insights and facilitate analysis of large datasets."}}}, "text-content-moderation": {"id": "text-content-moderation", "name": "Text Content Moderation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Scans and identifies potentially harmful, offensive, or inappropriate textual content, ensuring safer user environments."}}}, "text-to-audio": {"id": "text-to-audio", "name": "Text to Audio", "description": null, "params": [{"name": "text", "code": "text", "required": true, "dataType": "text", "dataSubType": "text"}, {"name": "language", "code": "language", "required": false, "dataType": "label", "dataSubType": "label"}], "output": [{"name": "data", "code": "data", "dataType": "audio"}], "metadata": {"modalities": ["text-audio"], "metaData": {"InputType": "text", "OutputType": "audio", "description": "The Text to Audio function converts written text into spoken words, allowing users to listen to the content instead of reading it."}}}, "video-embedding": {"id": "video-embedding", "name": "Video Embedding", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "video", "code": "video", "required": false, "dataType": "video", "dataSubType": "video"}], "output": [{"name": "data", "code": "data", "dataType": "embedding"}], "metadata": {"modalities": ["video-embedding"], "metaData": {"InputType": "video", "OutputType": "embedding", "description": "Video Embedding is a process that transforms video content into a fixed-dimensional vector representation, capturing essential features and patterns to facilitate tasks such as retrieval, classification, and recommendation."}}}, "dialect-detection": {"id": "dialect-detection", "name": "Dialect Detection", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Identifies specific dialects within a language, aiding in localized content creation or user experience personalization."}}}, "speech-non-speech-classification": {"id": "speech-non-speech-classification", "name": "Speech or Non-Speech Classification", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Differentiates between speech and non-speech audio segments. Great for editing software and transcription services to exclude irrelevant audio."}}}, "noise-removal": {"id": "noise-removal", "name": "Noise Removal", "description": null, "params": [{"name": "audio", "code": "audio", "required": false, "dataType": "audio", "dataSubType": "audio"}], "output": [{"name": "data", "code": "data", "dataType": "audio"}], "metadata": {"modalities": ["audio-audio"], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "Noise Removal is a process that involves identifying and eliminating unwanted random variations or disturbances from an audio signal to enhance the clarity and quality of the underlying information."}}}, "fill-text-mask": {"id": "fill-text-mask", "name": "Fill Text Mask", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "json", "multipleValues": true}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Completes missing parts of a text based on the context, ideal for content generation or data augmentation tasks."}}}, "activity-detection": {"id": "activity-detection", "name": "Activity Detection", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "label", "description": "detection of the presence or absence of human speech, used in speech processing."}}}, "select-supplier-for-translation": {"id": "select-supplier-for-translation", "name": "Select Supplier For Translation", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": false, "dataType": "label", "dataSubType": "label"}, {"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "Supplier", "code": "data", "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Supplier For Translation"}}}, "expression-detection": {"id": "expression-detection", "name": "Expression Detection", "description": null, "params": [{"name": "Media", "code": "media", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["image-label", "video-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Expression Detection is the process of identifying and analyzing facial expressions to interpret emotions or intentions using AI and computer vision techniques."}}}, "visual-question-answering": {"id": "visual-question-answering", "name": "Visual Question Answering", "description": null, "params": [{"name": "text", "code": "text", "required": true, "dataType": "text", "dataSubType": "text"}, {"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["image|text-text"], "metaData": {"InputType": "image", "OutputType": "video", "description": "Visual Question Answering (VQA) is a task in artificial intelligence that involves analyzing an image and providing accurate, contextually relevant answers to questions posed about the visual content of that image."}}}, "connection": {"id": "connection", "name": "Connection", "description": null, "params": [{"name": "Name", "code": "name", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Connections are integration that allow you to connect your AI agents to external tools"}}}, "summarization": {"id": "summarization", "name": "Summarization", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "Text summarization is the process of distilling the most important information from a source (or sources) to produce an abridged version for a particular user (or users) and task (or tasks)"}}}, "video-generation": {"id": "video-generation", "name": "Video Generation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Video", "code": "data", "defaultValue": [], "dataType": "video"}], "metadata": {"modalities": ["text-video"], "metaData": {"InputType": "text", "OutputType": "video", "description": "Produces video content based on specific inputs or datasets. Can be used for simulations, animations, or even deepfake detection."}}}, "instance-segmentation": {"id": "instance-segmentation", "name": "Instance Segmentation", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Instance segmentation is a computer vision task that involves detecting and delineating each distinct object within an image, assigning a unique label and precise boundary to every individual instance of objects, even if they belong to the same category."}}}, "voice-cloning": {"id": "voice-cloning", "name": "Voice Cloning", "description": null, "params": [{"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Voice", "code": "voice", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Type", "code": "type", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target Audio", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["audio|text-audio"], "metaData": {"InputType": "text", "OutputType": "audio", "description": "Replicates a person's voice based on a sample, allowing for the generation of speech in that person's tone and style. Used cautiously due to ethical considerations."}}}, "multilingual-speech-recognition": {"id": "multilingual-speech-recognition", "name": "Multilingual Speech Recognition", "description": null, "params": [{"name": "source_audio", "code": "source_audio", "required": true, "dataType": "audio", "dataSubType": "audio"}, {"name": "language", "code": "language", "required": false, "dataType": "label", "dataSubType": "label"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["audio-text"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Multilingual Speech Recognition is a technology that enables the automatic transcription of spoken language into text across multiple languages, allowing for seamless communication and understanding in diverse linguistic contexts."}}}, "video-content-moderation": {"id": "video-content-moderation", "name": "Video Content Moderation", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}, {"name": "Min Confidence", "code": "min_confidence", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["video-label"], "metaData": {"InputType": "video", "OutputType": "label", "description": "Automatically reviews video content to detect and possibly remove inappropriate or harmful material. Essential for user-generated content platforms."}}}, "named-entity-recognition": {"id": "named-entity-recognition", "name": "Named Entity Recognition", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Domain", "code": "domain", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Identifies and classifies named entities (e.g., persons, organizations, locations) within text. Useful for information extraction, content tagging, and search enhancements."}}}, "image-analysis": {"id": "image-analysis", "name": "Image Analysis", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "image", "OutputType": "label", "description": "Image analysis is the extraction of meaningful information from images"}}}, "utilities": {"id": "utilities", "name": "Utilites", "description": null, "params": [{"name": "Inputs", "code": "inputs", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Outputs", "code": "outputs", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": ""}}}, "split-on-silence": {"id": "split-on-silence", "name": "Split On Silence", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Segments", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["audio-audio"], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "The \"Split On Silence\" function divides an audio recording into separate segments based on periods of silence, allowing for easier editing and analysis of individual sections."}}}, "video-label-detection": {"id": "video-label-detection", "name": "Video Label Detection", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}, {"name": "Min Confidence", "code": "min_confidence", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["video-label"], "metaData": {"InputType": "video", "OutputType": "label", "description": "Identifies and tags objects, scenes, or activities within a video. Useful for content indexing and recommendation systems."}}}, "asr-quality-estimation": {"id": "asr-quality-estimation", "name": "ASR Quality Estimation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "json", "multipleValues": true, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "ASR Quality Estimation is a process that evaluates the accuracy and reliability of automatic speech recognition systems by analyzing their performance in transcribing spoken language into text."}}}, "entity-sentiment-analysis": {"id": "entity-sentiment-analysis", "name": "Entity Sentiment Analysis", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Entity Sentiment Analysis combines both entity analysis and sentiment analysis and attempts to determine the sentiment (positive or negative) expressed about entities within the text."}}}, "document-information-extraction": {"id": "document-information-extraction", "name": "Document Information Extraction", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["image-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Document Information Extraction is the process of automatically identifying, extracting, and structuring relevant data from unstructured or semi-structured documents, such as invoices, receipts, contracts, and forms, to facilitate easier data management and analysis."}}}, "text-detection": {"id": "text-detection", "name": "Text Detection", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "image", "OutputType": "text ", "description": "detect text regions in the complex background and label them with bounding boxes."}}}, "image-and-video-analysis": {"id": "image-and-video-analysis", "name": "Image and Video Analysis", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "image", "OutputType": "text", "description": ""}}}, "keyword-extraction": {"id": "keyword-extraction", "name": "Keyword Extraction", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "It helps concise the text and obtain relevant keywords Example use-cases are finding topics of interest from a news article and identifying the problems based on customer reviews and so."}}}, "ocr": {"id": "ocr", "name": "OCR", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}, {"name": "Feature Types", "code": "featuretypes", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["image-text", "document-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Converts images of typed, handwritten, or printed text into machine-encoded text. Used in digitizing printed texts for data retrieval."}}}, "image-embedding": {"id": "image-embedding", "name": "Image Embedding", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["image-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Image Embedding is a process that transforms an image into a fixed-dimensional vector representation, capturing its essential features and enabling efficient comparison, retrieval, and analysis in various machine learning and computer vision tasks."}}}, "intent-classification": {"id": "intent-classification", "name": "Intent Classification", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Intent Classification is a natural language processing task that involves analyzing and categorizing user text input to determine the underlying purpose or goal behind the communication, such as booking a flight, asking for weather information, or setting a reminder."}}}, "zero-shot-classification": {"id": "zero-shot-classification", "name": "Zero-Shot Classification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Script In", "code": "script_in", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": ""}}}, "asr-age-classification": {"id": "asr-age-classification", "name": "ASR Age Classification", "description": null, "params": [{"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "label", "description": "The ASR Age Classification function is designed to analyze audio recordings of speech to determine the speaker's age group by leveraging automatic speech recognition (ASR) technology and machine learning algorithms."}}}, "benchmark-scoring-mt": {"id": "benchmark-scoring-mt", "name": "Benchmark Scoring MT", "description": null, "params": [{"name": "input", "code": "input", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "output", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "reference", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Benchmark Scoring MT is a function designed to evaluate and score machine translation systems by comparing their output against a set of predefined benchmarks, thereby assessing their accuracy and performance."}}}, "image-to-video-generation": {"id": "image-to-video-generation", "name": "Image To Video Generation", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "video"}], "metadata": {"modalities": ["image-video"], "metaData": {"InputType": "image", "OutputType": "video", "description": "The Image To Video Generation function transforms a series of static images into a cohesive, dynamic video sequence, often incorporating transitions, effects, and synchronization with audio to create a visually engaging narrative."}}}, "image-colorization": {"id": "image-colorization", "name": "Image Colorization", "description": null, "params": [{"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "image", "code": "image", "dataType": "image"}], "metadata": {"modalities": ["image-image"], "metaData": {"InputType": "image", "OutputType": "image", "description": "Image colorization is a process that involves adding color to grayscale images, transforming them from black-and-white to full-color representations, often using advanced algorithms and machine learning techniques to predict and apply the appropriate hues and shades."}}}, "text-summarization": {"id": "text-summarization", "name": "Text summarization", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Extracts the main points from a larger body of text, producing a concise summary without losing the primary message."}}}, "classification-metric": {"id": "classification-metric", "name": "Classification Metric", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": true, "defaultValues": []}, {"name": "References", "code": "references", "required": true, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": true, "defaultValues": []}, {"name": "Lower Is Better", "code": "lowerIsBetter", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "number"}], "metadata": {"modalities": ["text|text|text-number", "text|text-number"], "metaData": {"InputType": "text", "OutputType": "text", "description": "A Classification Metric is a quantitative measure used to evaluate the quality and effectiveness of classification models."}}}, "referenceless-text-generation-metric": {"id": "referenceless-text-generation-metric", "name": "Referenceless Text Generation Metric", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["text|text-number"], "metaData": {"InputType": "text", "OutputType": "text", "description": "The Referenceless Text Generation Metric is a method for evaluating the quality of generated text without requiring a reference text for comparison, often leveraging models or algorithms to assess coherence, relevance, and fluency based on intrinsic properties of the text itself."}}}, "intent-recognition": {"id": "intent-recognition", "name": "Intent Recognition", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "text", "description": "classify the user's utterance (provided in varied natural language)  or text into one of several predefined classes, that is, intents."}}}, "text-generation-metric": {"id": "text-generation-metric", "name": "Text Generation Metric", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "References", "code": "references", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["text|text|text-number", "text|text-number"], "metaData": {"InputType": "text", "OutputType": "text", "description": "A Text Generation Metric is a quantitative measure used to evaluate the quality and effectiveness of text produced by natural language processing models, often assessing aspects such as coherence, relevance, fluency, and adherence to given prompts or instructions."}}}, "depth-estimation": {"id": "depth-estimation", "name": "Depth Estimation", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "image", "code": "image", "required": false, "dataType": "image", "dataSubType": "image"}], "output": [{"name": "data", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["image-text"], "metaData": {"InputType": "image", "OutputType": "text", "description": "Depth estimation is a computational process that determines the distance of objects from a viewpoint, typically using visual data from cameras or sensors to create a three-dimensional understanding of a scene."}}}, "connector": {"id": "connector", "name": "Connector", "description": null, "params": [{"name": "Name", "code": "name", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Connectors are integration that allow you to connect your AI agents to external tools"}}}, "referenceless-text-generation-metric-default": {"id": "referenceless-text-generation-metric-default", "name": "Referenceless Text Generation Metric <PERSON>", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The Referenceless Text Generation Metric Default is a function designed to evaluate the quality of generated text without relying on reference texts for comparison."}}}, "token-classification": {"id": "token-classification", "name": "Token Classification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Token-level classification means that each token will be given a label, for example a part-of-speech tagger will classify each word as one particular part of speech."}}}, "speaker-recognition": {"id": "speaker-recognition", "name": "Speaker Recognition", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "audio", "OutputType": "label", "description": "In speaker identification, an utterance from an unknown speaker is analyzed and compared with speech models of known speakers."}}}, "subtitling-translation": {"id": "subtitling-translation", "name": "Subtitling Translation", "description": null, "params": [{"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false}, {"name": "Source Language", "code": "sourcelanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "Dialect In", "code": "dialect_in", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "Machine Translation Supplier", "code": "target_supplier", "required": false, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": [], "availableOptions": [{"value": "aws", "label": "AWS"}, {"value": "azure", "label": "Azure"}, {"value": "modernmt", "label": "ModernMT"}, {"value": "apptek", "label": "AppTek"}, {"value": "google", "label": "Google"}]}, {"name": "Target Languages", "code": "targetlanguages", "required": false, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": true, "defaultValues": [], "availableOptions": [{"value": "en", "label": "English"}, {"value": "de", "label": "German"}, {"value": "nl", "label": "Dutch"}, {"value": "fr", "label": "French"}, {"value": "el", "label": "Greek"}, {"value": "it", "label": "Italian"}, {"value": "pl", "label": "Polish"}, {"value": "pt", "label": "Portuguese"}, {"value": "ru", "label": "Russian"}, {"value": "es", "label": "Spanish"}, {"value": "zh", "label": "Chinese"}, {"value": "sl", "label": "Slovenian"}, {"value": "ar", "label": "Arabic"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "tr", "label": "Turkish"}]}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["document-document"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Converts the text of subtitles from one language to another, ensuring context and cultural nuances are maintained. Essential for global content distribution."}}}, "audio-transcript-improvement": {"id": "audio-transcript-improvement", "name": "Audio Transcript Improvement", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "ASR Supplier", "code": "source_supplier", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "availableOptions": []}, {"name": "Is Medical", "code": "is_medical", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "boolean", "multipleValues": false, "availableOptions": []}, {"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-text"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Refines and corrects transcriptions generated from audio data, improving readability and accuracy."}}}, "image-compression": {"id": "image-compression", "name": "Image Compression", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}, {"name": "apl_qfactor", "code": "apl_qfactor", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "80", "label": "80"}]}], "output": [{"name": "image", "code": "image", "defaultValue": [], "dataType": "image"}], "metadata": {"modalities": ["image-image"], "metaData": {"InputType": "image", "OutputType": "image", "description": "Reduces the size of image files without significantly compromising their visual quality. Useful for optimizing storage and improving webpage load times."}}}, "audio-forced-alignment": {"id": "audio-forced-alignment", "name": "Audio Forced Alignment", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "text", "defaultValue": [], "dataType": "text"}, {"name": "Audio", "code": "audio", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["audio|text-audio|text"], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "Synchronizes phonetic and phonological text with the corresponding segments in an audio file. Useful in linguistic research and detailed transcription tasks."}}}, "emotion-detection": {"id": "emotion-detection", "name": "Emotion Detection", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Identifies human emotions from text or audio, enhancing user experience in chatbots or customer feedback analysis."}}}, "audio-generation-metric": {"id": "audio-generation-metric", "name": "Audio Generation Metric", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": true, "defaultValues": []}, {"name": "References", "code": "references", "required": false, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": ["audio|audio|text-number", "audio|audio-number"], "metaData": {"InputType": "text", "OutputType": "text", "description": "The Audio Generation Metric is a quantitative measure used to evaluate the quality, accuracy, and overall performance of audio generated by artificial intelligence systems, often considering factors such as fidelity, intelligibility, and similarity to human-produced audio."}}}, "syntax-analysis": {"id": "syntax-analysis", "name": "Syntax Analysis", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "Is the process of analyzing natural language with the rules of a formal grammar. Grammatical rules are applied to categories and groups of words, not individual words. Syntactic analysis basically assigns a semantic structure to text."}}}, "voice-activity-detection": {"id": "voice-activity-detection", "name": "Voice Activity Detection", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Onset", "code": "onset", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}, {"name": "Offset", "code": "offset", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}, {"name": "Min Duration On", "code": "min_duration_on", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "1", "label": "1"}]}, {"name": "Min Duration Off", "code": "min_duration_off", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}], "output": [{"name": "Audio", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["audio-audio"], "metaData": {"InputType": "audio", "OutputType": "audio", "description": "Determines when a person is speaking in an audio clip. It's an essential preprocessing step for other audio-related tasks."}}}, "image-label-detection": {"id": "image-label-detection", "name": "Image Label Detection", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}, {"name": "Min Confidence", "code": "min_confidence", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Identifies objects, themes, or topics within images, useful for image categorization, search, and recommendation systems."}}}, "speech-synthesis": {"id": "speech-synthesis", "name": "Speech Synthesis", "description": null, "params": [{"name": "Audio", "code": "audio", "required": false, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Voice", "code": "voice", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Type", "code": "type", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target Audio", "code": "data", "defaultValue": [], "dataType": "audio"}], "metadata": {"modalities": ["text-audio"], "metaData": {"InputType": "text", "OutputType": "audio", "description": "Generates human-like speech from written text. Ideal for text-to-speech applications, audiobooks, and voice assistants."}}}, "sentiment-analysis": {"id": "sentiment-analysis", "name": "Sentiment Analysis", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Determines the sentiment or emotion (e.g., positive, negative, neutral) of a piece of text, aiding in understanding user feedback or market sentiment."}}}, "video-understanding": {"id": "video-understanding", "name": "Video Understanding", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}, {"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "text", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text|video-text|video"], "metaData": {"InputType": "video", "OutputType": "text", "description": "Video Understanding is the process of analyzing and interpreting video content to extract meaningful information, such as identifying objects, actions, events, and contextual relationships within the footage."}}}, "diacritization": {"id": "diacritization", "name": "Diacritization", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Adds diacritical marks to text, essential for languages where meaning can change based on diacritics."}}}, "video-forced-alignment": {"id": "video-forced-alignment", "name": "Video Forced Alignment", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}, {"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "text", "defaultValue": [], "dataType": "text"}, {"name": "Video", "code": "video", "defaultValue": [], "dataType": "video"}], "metadata": {"modalities": ["text|video-text|video"], "metaData": {"InputType": "video", "OutputType": "video", "description": "Aligns the transcription of spoken content in a video with its corresponding timecodes, facilitating subtitle creation."}}}, "topic-classification": {"id": "topic-classification", "name": "Topic Classification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Assigns categories or topics to a piece of text based on its content, facilitating content organization and retrieval."}}}, "image-content-moderation": {"id": "image-content-moderation", "name": "Image Content Moderation", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}, {"name": "Min Confidence", "code": "min_confidence", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "number", "multipleValues": false, "defaultValues": [{"value": "0.5", "label": "0.5"}]}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["image-label"], "metaData": {"InputType": "image", "OutputType": "label", "description": "Detects and filters out inappropriate or harmful images, essential for platforms with user-generated visual content."}}}, "speaker-diarization-audio": {"id": "speaker-diarization-audio", "name": "Speaker Diarization Audio", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Segments", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["audio-label"], "metaData": {"InputType": "audio", "OutputType": "label", "description": "Identifies individual speakers and their respective speech segments within an audio clip. Ideal for multi-speaker recordings or conference calls."}}}, "search": {"id": "search", "name": "Search", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "An algorithm that identifies and returns data or items that match particular keywords or conditions from a dataset. A fundamental tool for databases and websites."}}}, "audio-transcript-analysis": {"id": "audio-transcript-analysis", "name": "Audio Transcript Analysis", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "ASR Supplier", "code": "source_supplier", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": [], "availableOptions": []}, {"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-text"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Analyzes transcribed audio data for insights, patterns, or specific information extraction."}}}, "speech-embedding": {"id": "speech-embedding", "name": "Speech Embedding", "description": null, "params": [{"name": "Audio", "code": "audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-embedding"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Transforms spoken content into a fixed-size vector in a high-dimensional space that captures the content's essence. Facilitates tasks like speech recognition and speaker verification."}}}, "speech-translation": {"id": "speech-translation", "name": "Speech Translation", "description": null, "params": [{"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "Source Language", "code": "sourcelanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Target Language", "code": "targetlanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Voice", "code": "voice", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-text"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Speech Translation is a technology that converts spoken language in real-time from one language to another, enabling seamless communication between speakers of different languages."}}}, "speaker-diarization-video": {"id": "speaker-diarization-video", "name": "Speaker Diarization Video", "description": null, "params": [{"name": "Video", "code": "video", "required": true, "isFixed": false, "dataType": "video", "dataSubType": "video", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "video"}], "metadata": {"modalities": ["video-label"], "metaData": {"InputType": "video", "OutputType": "label", "description": "Segments a video based on different speakers, identifying when each individual speaks. Useful for transcriptions and understanding multi-person conversations."}}}, "text-classification": {"id": "text-classification", "name": "Text Classification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "label", "description": "Categorizes text into predefined groups or topics, facilitating content organization and targeted actions."}}}, "text-denormalization": {"id": "text-denormalization", "name": "Text Denormalization", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "To Lower Case", "code": "lowercase_latin", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "boolean", "multipleValues": false, "defaultValues": [{"value": "0", "label": "No"}], "availableOptions": []}, {"name": "Remove Accents", "code": "remove_accents", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "boolean", "multipleValues": false, "defaultValues": [{"value": "1", "label": "Yes"}], "availableOptions": []}, {"name": "Remove Punctuation", "code": "remove_punctuation", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "boolean", "multipleValues": false, "defaultValues": [{"value": "0", "label": "No"}], "availableOptions": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Converts standardized or normalized text into its original, often more readable, form. Useful in natural language generation tasks."}}}, "object-detection": {"id": "object-detection", "name": "Object Detection", "description": null, "params": [{"name": "Image", "code": "image", "required": true, "isFixed": false, "dataType": "image", "dataSubType": "image", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-number"], "metaData": {"InputType": "video", "OutputType": "text", "description": "Object Detection is a computer vision technology that identifies and locates objects within an image, typically by drawing bounding boxes around the detected objects and classifying them into predefined categories."}}}, "translation": {"id": "translation", "name": "Translation", "description": null, "params": [{"name": "Source Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Source Language", "code": "sourcelanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Target Language", "code": "targetlanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Script In", "code": "script_in", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Script Out", "code": "script_out", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect In", "code": "dialect_in", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect Out", "code": "dialect_out", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Context", "code": "context", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target Text", "code": "data", "defaultValue": true, "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Converts text from one language to another while maintaining the original message's essence and context. Crucial for global communication."}}}, "text-segmenation": {"id": "text-segmenation", "name": "Text Segmentation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "json", "multipleValues": true}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Text Segmentation is the process of dividing a continuous text into meaningful units, such as words, sentences, or topics, to facilitate easier analysis and understanding."}}}, "multi-label-text-classification": {"id": "multi-label-text-classification", "name": "Multi Label Text Classification", "description": null, "params": [{"name": "language", "code": "language", "required": true, "dataType": "label", "dataSubType": "label"}, {"name": "text", "code": "text", "required": false, "dataType": "text", "dataSubType": "text"}], "output": [{"name": "data", "code": "data", "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Multi Label Text Classification is a natural language processing task where a given text is analyzed and assigned multiple relevant labels or categories from a predefined set, allowing for the text to belong to more than one category simultaneously."}}}, "text-spam-detection": {"id": "text-spam-detection", "name": "Text Spam Detection", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Identifies and filters out unwanted or irrelevant text content, ideal for moderating user-generated content or ensuring quality in communication platforms."}}}, "other-(multipurpose)": {"id": "other-(multipurpose)", "name": "Other (Multipurpose)", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The \"Other (Multipurpose)\" function serves as a versatile category designed to accommodate a wide range of tasks and activities that do not fit neatly into predefined classifications, offering flexibility and adaptability for various needs."}}}, "offensive-language-identification": {"id": "offensive-language-identification", "name": "Offensive Language Identification", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-label"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Detects language or phrases that might be considered offensive, aiding in content moderation and creating respectful user interactions."}}}, "text-generation-metric-default": {"id": "text-generation-metric-default", "name": "Text Generation Metric <PERSON>", "description": null, "params": [{"name": "Hypotheses", "code": "hypotheses", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "References", "code": "references", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Sources", "code": "sources", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": []}, {"name": "Score Identifier", "code": "score_identifier", "required": true, "isFixed": true, "dataType": "text", "dataSubType": "text", "multipleValues": false}], "output": [{"name": "Score", "code": "data", "dataType": "text"}], "metadata": {"modalities": [], "metaData": {"InputType": "text", "OutputType": "text", "description": "The \"Text Generation Metric Default\" function provides a standard set of evaluation metrics for assessing the quality and performance of text generation models."}}}, "text-normalization": {"id": "text-normalization", "name": "Text Normalization", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false}, {"name": "Language", "code": "language", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "Settings", "code": "settings", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": true, "defaultValues": [], "availableOptions": [{"value": "remove urls", "label": "remove urls"}, {"value": "remove emails", "label": "remove emails"}, {"value": "remove phone numbers", "label": "remove phone numbers"}, {"value": "remove emojis", "label": "remove emojis"}, {"value": "remove html tags", "label": "remove html tags"}, {"value": "normalize quotes", "label": "normalize quotes"}, {"value": "lowercase text", "label": "lowercase text"}, {"value": "remove default arabic diacritics", "label": "remove default arabic diacritics"}, {"value": "remove full arabic diacritics", "label": "remove full arabic diacritics"}, {"value": "normalize default arabic", "label": "normalize default arabic"}, {"value": "normalize full arabic", "label": "normalize full arabic"}, {"value": "remove arabic superfluous", "label": "remove arabic superfluous"}, {"value": "remove kashida dagger", "label": "remove kashida dagger"}, {"value": "normalize spoken text", "label": "normalize spoken text"}, {"value": "denormalize spoken text", "label": "denormalize spoken text"}, {"value": "tokenize text", "label": "tokenize text"}]}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-text"], "metaData": {"InputType": "text", "OutputType": "label", "description": "Converts unstructured or non-standard textual data into a more readable and uniform format, dealing with abbreviations, numerals, and other non-standard words."}}}, "text-generation": {"id": "text-generation", "name": "Text Generation", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}, {"name": "Temperature", "code": "temperature", "required": false, "isFixed": false, "dataType": "number", "dataSubType": "number", "multipleValues": false, "defaultValues": []}, {"name": "Prompt", "code": "prompt", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false}, {"name": "Context", "code": "context", "required": false, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false}, {"name": "Language", "code": "language", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["text-text", "image|text-text"], "metaData": {"InputType": "text", "OutputType": "text", "description": "Creates coherent and contextually relevant textual content based on prompts or certain parameters. Useful for chatbots, content creation, and data augmentation."}}}, "subtitling": {"id": "subtitling", "name": "Subtitling", "description": null, "params": [{"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false}, {"name": "Source Language", "code": "sourcelanguage", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "Dialect In", "code": "dialect_in", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false}, {"name": "Speech Recognition Supplier", "code": "source_supplier", "required": false, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": [{"value": "aws", "label": "AWS"}], "availableOptions": [{"value": "aws", "label": "AWS"}, {"value": "azure", "label": "Azure"}, {"value": "google", "label": "Google"}, {"value": "deepgram", "label": "Deepgram"}, {"value": "revai", "label": "Rev.<PERSON>"}, {"value": "apptek", "label": "AppTek"}, {"value": "openai", "label": "OpenAI"}]}, {"name": "Machine Translation Supplier", "code": "target_supplier", "required": false, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": [], "availableOptions": [{"value": "aws", "label": "AWS"}, {"value": "azure", "label": "Azure"}, {"value": "modernmt", "label": "ModernMT"}, {"value": "apptek", "label": "AppTek"}, {"value": "google", "label": "Google"}]}, {"name": "Target Languages", "code": "targetlanguages", "required": false, "isFixed": false, "dataType": "label", "dataSubType": "label", "multipleValues": true, "defaultValues": [], "availableOptions": [{"value": "en", "label": "English"}, {"value": "de", "label": "German"}, {"value": "nl", "label": "Dutch"}, {"value": "fr", "label": "French"}, {"value": "el", "label": "Greek"}, {"value": "it", "label": "Italian"}, {"value": "pl", "label": "Polish"}, {"value": "pt", "label": "Portuguese"}, {"value": "ru", "label": "Russian"}, {"value": "es", "label": "Spanish"}, {"value": "zh", "label": "Chinese"}, {"value": "sl", "label": "Slovenian"}, {"value": "ar", "label": "Arabic"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "tr", "label": "Turkish"}]}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-document"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Generates accurate subtitles for videos, enhancing accessibility for diverse audiences."}}}, "speech-recognition": {"id": "speech-recognition", "name": "Speech Recognition", "description": null, "params": [{"name": "Language", "code": "language", "required": true, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Dialect", "code": "dialect", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Voice", "code": "voice", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}, {"name": "Source Audio", "code": "source_audio", "required": true, "isFixed": false, "dataType": "audio", "dataSubType": "audio", "multipleValues": false, "defaultValues": []}, {"name": "<PERSON><PERSON><PERSON>", "code": "script", "required": false, "isFixed": true, "dataType": "label", "dataSubType": "label", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Target text", "code": "data", "defaultValue": [], "dataType": "text"}], "metadata": {"modalities": ["audio-text"], "metaData": {"InputType": "audio", "OutputType": "text", "description": "Converts spoken language into written text. Useful for transcription services, voice assistants, and applications requiring voice-to-text capabilities."}}}, "guardrails": {"id": "guardrails", "name": "Guardrails", "description": null, "params": [{"name": "Text", "code": "text", "required": true, "isFixed": false, "dataType": "text", "dataSubType": "text", "multipleValues": false, "defaultValues": []}], "output": [{"name": "Label", "code": "data", "defaultValue": [], "dataType": "label"}], "metadata": {"modalities": ["text-text", "text-label"], "metaData": {"InputType": "text", "OutputType": "text", "description": " Guardrails are governance rules that enforce security, compliance, and operational best practices, helping prevent mistakes and detect suspicious activity"}}}}}
import os
import requests
import re
import ast
from dotenv import load_dotenv
import google.generativeai as genai

load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

from flask import Flask, request, jsonify
from flask_cors import CORS
from langdetect import detect

genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-1.5-flash')

app = Flask(__name__)
CORS(app)

def remove_markdown(text):
    text = re.sub(r'\*\*.*?\*\*', '', text)
    text = re.sub(r'[\*\-] ', '', text)
    text = re.sub(r'[#\*_\[\]()]', '', text)
    text = re.sub(r'\n+', '\n', text).strip()
    return text

def format_text(text):
    sections = text.split("\n")
    return "\n\n".join(section.strip() for section in sections if section.strip())

def clean_and_format_response(raw_response):
    if "data=" in raw_response:
        raw_response = raw_response.split("data=")[-1].strip()
    raw_response = raw_response.strip("()'")
    try:
        raw_response = ast.literal_eval(f"'''{raw_response}'''")
    except Exception:
        pass
    match = re.search(r"https?://\S+\nSource:.*?\nDate: .*?\n\n", raw_response, re.DOTALL)
    if match:
        articles_part = raw_response[:match.end()].strip()
        summary_part = raw_response[match.end():].strip()
    else:
        return raw_response.strip()
    formatted_articles = re.sub(r"\n{3,}", "\n\n", articles_part)
    formatted_summary = re.sub(r"\n{3,}", "\n\n", summary_part)
    return f"{formatted_articles}\n\n{'-'*100}\n\n{formatted_summary}"

def get_nearest_health_centers(latitude, longitude):
    lat_offset = 0.09  
    lon_offset = 0.09  
    import math
    lon_offset = 0.09 / math.cos(math.radians(latitude))
    
    # Define bounding box
    south = latitude - lat_offset
    north = latitude + lat_offset
    west = longitude - lon_offset
    east = longitude + lon_offset

    search_queries = [
       
        f"https://nominatim.openstreetmap.org/search?format=json&amenity=hospital&bounded=1&viewbox={west},{north},{east},{south}&limit=10",
        
        f"https://nominatim.openstreetmap.org/search?format=json&amenity=clinic&bounded=1&viewbox={west},{north},{east},{south}&limit=10",
       
        f"https://nominatim.openstreetmap.org/search?format=json&amenity=doctors&bounded=1&viewbox={west},{north},{east},{south}&limit=10",
        
        f"https://nominatim.openstreetmap.org/search?format=json&healthcare=*&bounded=1&viewbox={west},{north},{east},{south}&limit=10",
    ]
    
    headers = {'User-Agent': 'GramAroghya-HealthApp/1.0'}
    all_results = []
    
    for search_url in search_queries:
        try:
            response = requests.get(search_url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                all_results.extend(data)
                # Add small delay to be respectful to the API
                import time
                time.sleep(0.1)
        except Exception as e:
            print(f"Search failed for {search_url}: {e}")
            continue
    
    if not all_results:

        try:
            reverse_url = f"https://nominatim.openstreetmap.org/reverse?format=json&lat={latitude}&lon={longitude}&zoom=10"
            reverse_response = requests.get(reverse_url, headers=headers)
            if reverse_response.status_code == 200:
                reverse_data = reverse_response.json()
                country = reverse_data.get("address", {}).get("country", "")
                if country:
                    # Search within the country
                    country_search_url = f"https://nominatim.openstreetmap.org/search?format=json&amenity=hospital&countrycodes={get_country_code(country)}&limit=10"
                    country_response = requests.get(country_search_url, headers=headers)
                    if country_response.status_code == 200:
                        all_results = country_response.json()
        except Exception as e:
            print(f"Country search failed: {e}")
    
    if not all_results:
        return {"error": "No health centers found nearby"}
    
    
    seen_locations = set()
    results = []
    
    for place in all_results:
        location_key = (float(place["lat"]), float(place["lon"]))
        if location_key not in seen_locations:
            seen_locations.add(location_key)
            
            
            distance = calculate_distance(latitude, longitude, float(place["lat"]), float(place["lon"]))
            
            
            if distance <= 50:
                name = place.get("display_name", "Unknown Health Center")
                
                facility_name = name.split(",")[0].strip()
                
                results.append({
                    "name": facility_name,
                    "address": place.get("display_name", "No address available"),
                    "latitude": float(place["lat"]),
                    "longitude": float(place["lon"]),
                    "distance": distance
                })
    
    
    results.sort(key=lambda x: x.get("distance", float('inf')))
    return results[:10]

def calculate_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two points using Haversine formula"""
    import math
    
    
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    
    r = 6371
    return c * r

def get_country_code(country_name):
    """Get ISO country code for common countries"""
    country_codes = {
        "India": "IN",
        "United States": "US", 
        "United Kingdom": "GB",
        "Canada": "CA",
        "Australia": "AU",
        "Germany": "DE",
        "France": "FR",
        "Japan": "JP",
        "China": "CN",
        "Brazil": "BR"
    }
    return country_codes.get(country_name, "IN")  

def get_route(start_lat, start_lon, end_lat, end_lon):
    
    url = f"https://router.project-osrm.org/route/v1/driving/{start_lon},{start_lat};{end_lon},{end_lat}?overview=full&geometries=polyline"
    headers = {'User-Agent': 'GramAroghya-HealthApp/1.0'}
    response = requests.get(url, headers=headers)
    data = response.json()
    
    if "routes" not in data or not data["routes"]:
        return {"error": "No route found"}
    
    route = data["routes"][0]
    return {
        "route_polyline": route["geometry"],
        "distance": route.get("distance", 0),
        "duration": route.get("duration", 0)
    }

@app.route("/ask", methods=["POST"])
def ask():
    try:
        data = request.json
        question = data.get("question", "")
        if not question:
            return jsonify({"error": "No question provided"}), 400
        output_language = detect(question)
        
        # Use Gemini for main health question answering
        main_prompt = f"""You are a knowledgeable health assistant. Answer the following health-related question in {output_language}. 
        Provide accurate, helpful information while being clear that this is for informational purposes only and not a substitute for professional medical advice.
        
        Question: {question}
        
        Please provide a comprehensive but concise answer."""
        
        main_response = gemini_model.generate_content(main_prompt)
        agent_answer = remove_markdown(main_response.text)
        agent_answer = format_text(agent_answer)
        
        # Use Gemini for summarization
        summary_prompt = f"Summarize the following health-related response in {output_language}. Keep it concise and focus on key points:\n\nQuestion: {question}\n\nResponse: {agent_answer}"
        summary_response = gemini_model.generate_content(summary_prompt)
        summary = remove_markdown(summary_response.text)
        summary = format_text(summary)
        
        return jsonify({"response": agent_answer, "summary": summary})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/doctors", methods=["POST"])
def find_doctors():
    try:
        data = request.json
        condition = data.get("condition", "")
        location = data.get("location", "")
        if not condition or not location:
            return jsonify({"error": "Condition and location required"}), 400
        
        # Use Gemini for doctor recommendations
        doctor_prompt = f"""Find and recommend doctors for the following:
        Medical Condition: {condition}
        Location: {location}
        
        Please provide a list of doctors with their specialties, clinic names, and contact information if available. 
        Format the response as a structured list with clear information for each doctor."""
        
        doctor_response = gemini_model.generate_content(doctor_prompt)
        doctors_text = remove_markdown(doctor_response.text)
        doctors_formatted = format_text(doctors_text)
        
        return jsonify({"doctors": doctors_formatted})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/health-centers", methods=["POST"])
def find_health_centers():
    try:
        data = request.json
        latitude = data.get("latitude")
        longitude = data.get("longitude")
        if not latitude or not longitude:
            return jsonify({"error": "Latitude and longitude are required"}), 400
        health_centers = get_nearest_health_centers(latitude, longitude)
        if "error" in health_centers:
            return jsonify(health_centers), 400
        if health_centers:
            first_center = health_centers[0]
            route = get_route(latitude, longitude, first_center["latitude"], first_center["longitude"])
            return jsonify({"nearest_health_centers": health_centers, "route": route})
        else:
            return jsonify({"error": "No health centers found nearby"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/news", methods=["POST"])
def get_news():
    try:
        data = request.json
        language = data.get("language", "")
        if not language:
            return jsonify({"error": "Language selection is required"}), 400
        
        # Use Gemini for health news with better formatting
        news_prompt = f"""Generate 5-6 recent health news articles in {language}. 
        
        Format each article EXACTLY like this:
        
        Title: [Article Title]
        Description: [Brief description in 1-2 lines]
        Content: [Detailed content about the health topic - 2-3 paragraphs]
        URL: https://example-health-news.com/article-{1}
        Source: Health Ministry / WHO / Medical Journal
        Date: 2025-01-30
        
        Include topics like:
        - Recent health developments in India
        - Public health announcements
        - Medical breakthroughs and research
        - Health tips and preventive care
        - Disease outbreaks or health alerts
        - Vaccination updates
        
        Make sure each article is relevant, informative, and follows the exact format above."""
        
        news_response = gemini_model.generate_content(news_prompt)
        news_text = news_response.text
        
        # Clean up the response but preserve structure
        cleaned_news = re.sub(r'\*\*|\*|#{1,6}\s*', '', news_text)
        cleaned_news = re.sub(r'\n{3,}', '\n\n', cleaned_news)
        
        return jsonify({"news": cleaned_news})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, port=5000)

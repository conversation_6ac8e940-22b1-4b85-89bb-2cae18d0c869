.container {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Updated align-items */
  min-height: calc(100vh - 3.5rem);
  padding: 4rem 1rem;
}

.wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  width: 90%;
  max-width: 1200px;
}

.card {
  flex: 1;
  background-color: #1a1a1a;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  text-align: left;
}

.inputCard {
  max-width: 500px;
}

.responseCard {
  max-width: 600px;
  overflow-y: auto;
  max-height: 500px;
  margin-bottom: 2rem; /* Updated margin-bottom */
}

.heading {
  padding-bottom: 1rem;
  font-size: 2rem;
  font-weight: bold;
  color: #fff;
}

.inputContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 1rem;
}

.input {
  width: 100%;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #333;
  background-color: #222;
  color: #fff;
  font-size: 1rem;
  outline: none;
}

.input:focus {
  border-color: #4da6ff;
  box-shadow: 0 0 5px rgba(77, 166, 255, 0.5);
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.findButton {
  width: 180px;
  background-color: #ffffff;
  color: #000000;
}

.findButton:hover {
  background-color: #b9b9b9;
}

.responseContainer {
  width: 100%;
  overflow-y: auto;
  max-height: 500px;
  padding-right: 10px;
}

.responseBox {
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #292929;
  border: 1px solid #444;
  overflow-y: auto;
}

.responseTitle {
  font-size: 1.2rem;
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.5rem;
}

.responseText {
  font-size: 1rem;
  color: #ddd;
}

.doctorCard {
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #292929;
  border: 1px solid #444;
  margin-bottom: 1rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.divider {
  border: none;
  height: 1px;
  background-color: #444;
  margin-top: 1rem;
}

/* Back to Home Button */
.homeButtonContainer {
  display: flex;
  justify-content: flex-start;
  width: 90%;
  max-width: 1200px;
  margin: 1rem auto;
}

.homeButton {
  width: 150px;
  background-color: #ffffff;
  color: #000000;
}

.homeButton:hover {
  background-color: #b9b9b9;
}

.mapContainer {
  width: 100%;
  height: 500px;
  margin-top: 2rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1200px;
}

@media (min-width: 1024px) {
  .wrapper {
    flex-direction: row;
    align-items: flex-start;
  }

  .inputCard,
  .responseCard {
    flex: 1;
  }

  .mapContainer {
    flex: 2;
    margin-left: 2rem;
  }
}


@echo off
echo Starting GramAroghya Application...
echo.
echo Starting Python Backend on port 5000...
start cmd /k "python backend.py"
echo.
echo Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak > nul
echo.
echo Testing location API...
python test_location.py
echo.
echo Testing news API...
python test_news.py
echo.
echo Testing ArogyaMitraAI...
python test_health_ai.py
echo.
echo Starting Next.js Frontend on port 3000...
start cmd /k "npm start"
echo.
echo Both servers are starting...
echo Frontend: http://localhost:3000
echo Backend: http://localhost:5000
echo.
echo Visit /g-map page to see improved healthcare locations!
echo.
echo Press any key to close this window...
pause > nul

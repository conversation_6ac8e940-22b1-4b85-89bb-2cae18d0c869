#!/usr/bin/env python3

import requests
import json

def test_health_centers(lat, lng, city_name):
    """Test the health centers API with given coordinates"""
    print(f"\n🏥 Testing health centers for {city_name} ({lat}, {lng})")
    print("-" * 60)
    
    url = "http://localhost:5000/health-centers"
    payload = {
        "latitude": lat,
        "longitude": lng
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        data = response.json()
        
        if response.status_code == 200:
            if "nearest_health_centers" in data:
                centers = data["nearest_health_centers"]
                print(f"✅ Found {len(centers)} health centers")
                
                for i, center in enumerate(centers[:3], 1):  # Show first 3
                    print(f"{i}. {center['name']}")
                    print(f"   📍 {center['address'][:80]}...")
                    if 'distance' in center:
                        print(f"   📏 {center['distance']:.2f} km away")
                    print()
                    
            elif "error" in data:
                print(f"❌ Error: {data['error']}")
            else:
                print(f"⚠️  Unexpected response format: {data}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {data}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure 'python backend.py' is running!")
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Testing GramAroghya Health Centers API")
    print("Make sure the backend is running with: python backend.py")
    
    # Test locations in India
    test_locations = [
        (19.0760, 72.8777, "Mumbai, India"),
        (28.6139, 77.2090, "New Delhi, India"),
        (12.9716, 77.5946, "Bangalore, India"),
        (22.5726, 88.3639, "Kolkata, India"),
        (13.0827, 80.2707, "Chennai, India")
    ]
    
    for lat, lng, city in test_locations:
        test_health_centers(lat, lng, city)
        
    print("\n🏁 Testing complete!")
    print("If you see healthcare facilities from other countries, the search needs improvement.")
    print("If you see 'Cannot connect' error, start the backend with: python backend.py")
